{"name": "searchsync", "version": "1.0.0", "description": "A unified search interface for multiple business applications", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "NODE_ENV=development electron .", "dev-win": "set NODE_ENV=development && electron .", "build": "electron-builder"}, "keywords": ["search", "jira", "azure-devops", "productivity"], "author": "", "license": "MIT", "devDependencies": {"electron": "^25.0.0", "electron-builder": "^24.4.0"}, "dependencies": {"@xenova/transformers": "^2.15.1", "axios": "^1.4.0", "electron-store": "^8.1.0"}, "resolutions": {"glob": "^8.1.0", "inflight": "^2.0.1", "boolean": "^3.2.0"}, "build": {"appId": "com.searchsync.app", "productName": "SearchSync", "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}
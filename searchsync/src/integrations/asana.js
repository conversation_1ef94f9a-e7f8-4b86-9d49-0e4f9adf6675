/**
 * Asana integration for SearchSync
 *
 * This module provides functionality to search for tasks in Asana.
 */

const axios = require('axios');

/**
 * Search Asana for tasks matching the query
 *
 * @param {string} query - The search query
 * @param {Object} config - Configuration for the Asana API
 * @param {string} config.personalAccessToken - Asana Personal Access Token
 * @param {string} [config.workspace] - Optional workspace ID to limit search to
 * @param {string} [config.project] - Optional project ID to limit search to
 * @returns {Promise<Array>} - Promise resolving to an array of search results
 */
async function searchAsana(query, config) {
  try {
    if (!config.personalAccessToken) {
      throw new Error('Asana Personal Access Token is required');
    }

    // Validate workspace GID if provided
    if (config.workspace && !/^\d+$/.test(config.workspace)) {
      console.warn(`Workspace GID "${config.workspace}" may be invalid. Asana workspace GIDs are typically numeric.`);
    }

    // Log config without sensitive information
    const loggableConfig = { ...config };
    delete loggableConfig.personalAccessToken;
    console.log(`Searching Asana with config: ${JSON.stringify(loggableConfig)}`);

    // Prepare search parameters
    let searchParams = {
      opt_fields: 'name,notes,completed,due_on,created_at,modified_at,permalink_url,assignee.name,projects.name,workspace.name',
      limit: 100
    };

    // Add project filter if provided
    if (config.project) {
      searchParams.project = config.project;
    }

    // Make the API request to search for tasks
    // According to Asana API documentation, we need to use the workspace parameter
    // and the correct parameter for search text is 'text'
    let url = 'https://app.asana.com/api/1.0/workspaces';
    let params = { ...searchParams };

    if (config.workspace) {
      // If workspace is provided, search within that workspace
      url = `${url}/${config.workspace}/tasks/search`;
    } else {
      // If no workspace is provided, first get the user's workspaces
      const workspacesResponse = await axios({
        method: 'get',
        url: 'https://app.asana.com/api/1.0/workspaces',
        headers: {
          'Authorization': `Bearer ${config.personalAccessToken}`,
          'Accept': 'application/json'
        }
      });

      if (!workspacesResponse.data.data || workspacesResponse.data.data.length === 0) {
        throw new Error('No workspaces found for the user');
      }

      // Use the first workspace
      const defaultWorkspace = workspacesResponse.data.data[0].gid;
      url = `${url}/${defaultWorkspace}/tasks/search`;
    }

    // Set the search text parameter
    params.text = query;

    // Make the API request
    const response = await axios({
      method: 'get',
      url: url,
      params: params,
      headers: {
        'Authorization': `Bearer ${config.personalAccessToken}`,
        'Accept': 'application/json'
      }
    });

    // Process the results
    const tasks = response.data.data || [];
    console.log(`Found ${tasks.length} Asana tasks matching "${query}"`);

    // Transform the results to the standard format
    const results = tasks.map(task => {
      // Format the result with standardized fields
      return {
        title: task.name,
        description: task.notes || '',
        url: task.permalink_url,
        source: 'Asana',
        type: 'task',
        status: task.completed ? 'completed' : 'active',
        updated: task.modified_at || task.created_at,
        metadata: {
          completed: task.completed,
          dueOn: task.due_on,
          assignee: task.assignee ? task.assignee.name : null,
          project: task.projects && task.projects.length > 0 ? task.projects[0].name : null,
          workspace: task.workspace ? task.workspace.name : null
        }
      };
    });

    // Return results without local sorting
    return {
      results: results
    };
  } catch (error) {
    console.error('Error searching Asana:', error);

    // Return a formatted error
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('API error response:', error.response.status);
      if (error.response.data) {
        console.error('Response data:', JSON.stringify(error.response.data));
      }
      throw new Error(`Asana API error: ${error.response.status} - ${error.response.data?.errors?.[0]?.message || error.message}`);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received:', error.request);
      throw new Error('Connection error: No response received from Asana');
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Request setup error:', error.message);
      throw error;
    }
  }
}

module.exports = {
  searchAsana
};
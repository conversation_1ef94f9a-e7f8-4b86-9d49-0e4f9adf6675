const { app, BrowserWindow, globalShortcut, ipcMain, shell, Tray, Menu } = require('electron');
const path = require('path');
const Store = require('electron-store');
const fs = require('fs');
const axios = require('axios');

// Integration imports
const jiraSearch = require('./integrations/jira');
const confluenceSearch = require('./integrations/confluence');
const azureSearch = require('./integrations/azure-devops');
const { searchAsana } = require('./integrations/asana');

// Initialize config store
const store = new Store({
  defaults: {
    shortcut: 'CommandOrControl+Shift+Space',
    minimizeToTray: true,
    integrations: {
      jira: {
        enabled: true,
        baseUrl: '',
        email: '',
        apiToken: '',
        searchShortcut: 'jsd'
      },
      confluence: {
        enabled: true,
        baseUrl: '',
        email: '',
        apiToken: '',
        space: '',
        searchShortcut: 'con'
      },
      azureDevops: {
        enabled: true,
        organization: '',
        project: '',
        personalAccessToken: '',
        searchShortcut: 'ado'
      },
      asana: {
        enabled: true,
        personalAccessToken: '',
        workspace: '',
        project: '',
        searchShortcut: 'as'
      }
    }
  }
});

let mainWindow;
let searchWindow;
let tray = null;

// Set quitting flag before the quit process begins to avoid minimizing to tray
app.on('before-quit', () => {
  app.isQuitting = true;
});


// Setup logging
const logPath = path.join(app.getPath('userData'), 'logs');

// Ensure log directory exists
if (!fs.existsSync(logPath)) {
  fs.mkdirSync(logPath, { recursive: true });
}

const logFile = path.join(logPath, 'searchsync.log');

function log(level, message, data = null) {
  const timestamp = new Date().toISOString();
  let logMessage = `[${timestamp}] [${level}] ${message}`;

  if (data) {
    try {
      if (data instanceof Error) {
        const sanitizedMessage = sanitizeString(data.message);
        const sanitizedStack = data.stack ? sanitizeString(data.stack) : '';
        logMessage += ` Error: ${sanitizedMessage}\nStack: ${sanitizedStack}`;
      } else if (typeof data === 'object') {
        const sanitizedData = JSON.parse(JSON.stringify(data));
        sanitizeObject(sanitizedData);
        try {
          logMessage += ` ${JSON.stringify(sanitizedData)}`;
        } catch (e) {
          logMessage += ` [Object cannot be stringified: ${e.message}]`;
        }
      } else if (typeof data === 'string') {
        logMessage += ` ${sanitizeString(data)}`;
      } else {
        logMessage += ` ${data}`;
      }
    } catch (error) {
      logMessage += ` [Error formatting log data: ${error.message}]`;
    }
  }

  logMessage = sanitizeString(logMessage);
  console.log(logMessage);

  try {
    fs.appendFileSync(logFile, logMessage + '\n');

    // Rotate log if > 5MB
    const stats = fs.statSync(logFile);
    if (stats.size > 5 * 1024 * 1024) {
      const backupFile = `${logFile}.old`;
      if (fs.existsSync(backupFile)) {
        fs.unlinkSync(backupFile);
      }
      fs.renameSync(logFile, backupFile);
      fs.writeFileSync(logFile, '');
    }
  } catch (error) {
    console.error(`Error managing log file: ${error.message}`);
  }
}

/**
 * Sanitize an object by recursively replacing sensitive values with '***'
 * @param {Object} obj - The object to sanitize
 */
function sanitizeObject(obj) {
  if (!obj || typeof obj !== 'object') return;

  // List of sensitive field names (case-insensitive)
  const sensitiveFields = [
    'apitoken', 'api_token', 'token', 'key', 'secret', 'password', 'pass', 'pwd',
    'auth', 'credential', 'personalaccesstoken', 'personal_access_token', 'pat',
    'authorization', 'apikey', 'api_key', 'access_token', 'accesstoken', 'secret_key',
    'secretkey', 'private_key', 'privatekey'
  ];

  // Process all properties in the object
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      // Check if the current key is sensitive (case-insensitive comparison)
      const keyLower = key.toLowerCase();
      if (sensitiveFields.some(field => keyLower.includes(field))) {
        // Mask sensitive values
        obj[key] = '***';
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        // Recursively sanitize nested objects
        sanitizeObject(obj[key]);
      } else if (typeof obj[key] === 'string') {
        // Check if the string value looks like a token/key
        obj[key] = sanitizeString(obj[key]);
      }
    }
  }
}

/**
 * Sanitize a string by masking potential API keys, tokens, and other sensitive data
 * @param {string} str - The string to sanitize
 * @returns {string} - Sanitized string
 */
function sanitizeString(str) {
  if (!str || typeof str !== 'string') return str;

  // Patterns that might indicate sensitive information
  const patterns = [
    // Basic auth pattern (username:password)
    /([a-zA-Z0-9_\-\.]+@[a-zA-Z0-9_\-\.]+\.[a-zA-Z]{2,5}:[a-zA-Z0-9_\-\.!@#$%^&*()]{3,})/g,

    // Common API key/token patterns (alphanumeric strings of certain lengths)
    /\b([a-zA-Z0-9_\-]{20,})\b/g,

    // OAuth tokens often start with specific prefixes
    /\b(Bearer\s+[a-zA-Z0-9_\.\-]+)/gi,
    /\b(Basic\s+[a-zA-Z0-9_\.\-=]+)/gi,

    // Azure DevOps PAT pattern
    /\b([a-z0-9]{52})\b/gi,

    // Jira API token pattern
    /\b([a-zA-Z0-9]{24})\b/g,

    // Base64 encoded credentials
    /\b([a-zA-Z0-9+/]{30,}={0,2})\b/g
  ];

  // Replace each pattern with asterisks
  let sanitized = str;
  patterns.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '***');
  });

  return sanitized;
}

function createTray() {
  if (tray) return; // Don't create tray if it already exists

  log('INFO', 'Creating tray icon');

  // Define icon paths
  const iconPaths = {
    win: [
      path.join(__dirname, '..', 'build', 'icon.ico'),
      path.join(__dirname, 'assets', 'icon.ico'),
      path.join(__dirname, 'assets', 'icon.png')
    ],
    other: [
      path.join(__dirname, 'assets', 'icon.png')
    ]
  };

  // Select platform-specific icon paths
  const paths = process.platform === 'win32' ? iconPaths.win : iconPaths.other;

  // Find first existing icon
  let iconPath = null;
  for (const p of paths) {
    if (fs.existsSync(p)) {
      iconPath = p;
      break;
    }
  }

  // Create fallback icon if needed
  if (!iconPath) {
    log('WARN', 'No tray icon found, creating fallback icon');
    const fallbackIconPath = path.join(__dirname, 'assets', 'fallback-icon.png');
    const assetsDir = path.dirname(fallbackIconPath);

    if (!fs.existsSync(assetsDir)) {
      fs.mkdirSync(assetsDir, { recursive: true });
    }

    // Create minimal transparent PNG if needed
    if (!fs.existsSync(fallbackIconPath)) {
      const fallbackIconData = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAATSURBVDhPYxgFo2AUjAIIYGAAAAQQAAGnRHxjAAAAAElFTkSuQmCC',
        'base64'
      );
      fs.writeFileSync(fallbackIconPath, fallbackIconData);
    }

    iconPath = fallbackIconPath;
  }

  log('INFO', `Using tray icon at path: ${iconPath}`);

  try {
    tray = new Tray(iconPath);
    tray.setToolTip('SearchSync');
  } catch (error) {
    log('ERROR', `Failed to create tray icon: ${error.message}`);
    return;
  }

  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'Open SearchSync',
      click: () => {
        if (mainWindow) {
          if (mainWindow.isMinimized()) mainWindow.restore();
          mainWindow.show();
          mainWindow.focus();
        } else {
          createMainWindow();
        }
      }
    },
    {
      label: 'Search',
      click: () => {
        createSearchWindow();
      }
    },
    { type: 'separator' },
    {
      label: 'Quit',
      click: () => {
        // Ensure quitting flag is set and then quit
        app.isQuitting = true;
        app.quit();
      }
    }
  ]);

  tray.setContextMenu(contextMenu);

  // Double-click on tray icon shows the main window
  tray.on('double-click', () => {
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.show();
      mainWindow.focus();
    } else {
      createMainWindow();
    }
  });

  // Single click on tray icon shows the main window (Windows only)
  if (process.platform === 'win32') {
    tray.on('click', () => {
      if (mainWindow) {
        if (mainWindow.isMinimized()) mainWindow.restore();
        mainWindow.show();
        mainWindow.focus();
      } else {
        createMainWindow();
      }
    });
  }
}

function createMainWindow() {
  log('INFO', 'Creating main window');
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  mainWindow.loadFile(path.join(__dirname, 'ui', 'settings.html'));

  // Open DevTools in development
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }

  // Handle minimize event
  mainWindow.on('minimize', (event) => {
    const minimizeToTray = store.get('minimizeToTray');
    if (minimizeToTray) {
      event.preventDefault();
      mainWindow.hide();
      if (!tray) createTray();
    }
  });

  // Handle close event
  mainWindow.on('close', (event) => {
    if (!app.isQuitting) {
      const minimizeToTray = store.get('minimizeToTray');
      if (minimizeToTray) {
        event.preventDefault();
        mainWindow.hide();
        if (!tray) createTray();
        return false;
      }
    }
    return true;
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

function createSearchWindow() {
  if (searchWindow) {
    searchWindow.focus();
    return;
  }

  log('INFO', 'Creating search window');
  searchWindow = new BrowserWindow({
    width: 600,
    height: 400,
    frame: false,
    resizable: true,
    alwaysOnTop: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  searchWindow.loadFile(path.join(__dirname, 'ui', 'search.html'));

  // Open DevTools in development
  if (process.env.NODE_ENV === 'development') {
    searchWindow.webContents.openDevTools();
  }

  searchWindow.on('blur', () => {
    searchWindow.close();
  });

  searchWindow.on('closed', () => {
    searchWindow = null;
  });
}

// Main application initialization
app.whenReady().then(async () => {
  log('INFO', 'Application ready, initializing');

  // Register global shortcuts
  const shortcut = store.get('shortcut') || 'CommandOrControl+Shift+Space';
  globalShortcut.register(shortcut, () => {
    createSearchWindow();
  });

  createMainWindow();

  // Create the tray icon if minimizeToTray is enabled
  if (store.get('minimizeToTray')) {
    createTray();
  }

  log('INFO', 'App initialization complete');
}).catch(error => {
  log('ERROR', 'Error during app initialization:', error);
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('will-quit', () => {
  // Unregister all shortcuts
  log('INFO', 'Application quitting, unregistering shortcuts');
  globalShortcut.unregisterAll();

  // Set flag to allow the app to quit
  app.isQuitting = true;

  // Destroy tray
  if (tray) {
    tray.destroy();
    tray = null;
  }

  // Clean up the model pipeline if it exists
  if (extractor) {
    log('INFO', 'Disposing of transformer model pipeline');
    extractor.dispose();
    extractor = null;
  }
});

// IPC handlers for search functionality
ipcMain.on('search', async (event, query, activeFilters = [], sortMode = 'relevance') => {
  log('INFO', `Search initiated with query: ${query}, Active Filters: ${activeFilters.join(', ')}, Sort Mode: ${sortMode}`);
  const integrations = store.get('integrations');

  // Create sanitized copies of the integration configs for logging
  const sanitizedIntegrations = JSON.parse(JSON.stringify(integrations));
  if (sanitizedIntegrations.jira) {
    if (sanitizedIntegrations.jira.apiToken) sanitizedIntegrations.jira.apiToken = '***';
    if (sanitizedIntegrations.jira.email) {
      sanitizedIntegrations.jira.email = sanitizedIntegrations.jira.email.replace(/^(.{2})(.*)(@.*)$/, '$1***$3');
    }
  }
  if (sanitizedIntegrations.confluence) {
    if (sanitizedIntegrations.confluence.apiToken) sanitizedIntegrations.confluence.apiToken = '***';
    if (sanitizedIntegrations.confluence.email) {
      sanitizedIntegrations.confluence.email = sanitizedIntegrations.confluence.email.replace(/^(.{2})(.*)(@.*)$/, '$1***$3');
    }
  }
  if (sanitizedIntegrations.azureDevops && sanitizedIntegrations.azureDevops.personalAccessToken) {
    sanitizedIntegrations.azureDevops.personalAccessToken = '***';
  }
  if (sanitizedIntegrations.asana && sanitizedIntegrations.asana.personalAccessToken) {
    sanitizedIntegrations.asana.personalAccessToken = '***';
  }

  const results = [];
  const errors = [];

  // Load and execute search for each enabled integration
  // Only search integrations that are both enabled in settings AND selected in active filters (or if no filters are active)
  if (integrations.jira.enabled && (activeFilters.length === 0 || activeFilters.includes('jira'))) {
    log('INFO', 'Starting Jira search', sanitizedIntegrations.jira);
    try {
      if (!integrations.jira.baseUrl || !integrations.jira.apiToken) {
        const error = 'Jira configuration is incomplete. Please check your settings.';
        log('ERROR', error);
        errors.push({ source: 'Jira', error });
      } else {
        // Add more detailed logging
        log('INFO', `Searching Jira for: "${query}"`);

        const jiraResults = await jiraSearch(query, integrations.jira);

        // Ensure jiraResults is always an array
        const validJiraResults = Array.isArray(jiraResults) ? jiraResults : [];

        log('INFO', `Jira search completed with ${validJiraResults.length} results`);

        if (validJiraResults.length === 0) {
          log('WARN', 'Jira search returned zero results. This might indicate a permission issue or configuration problem.');
        } else {
          // Log the first result for debugging
          log('INFO', `First Jira result: ${validJiraResults[0].id} - ${validJiraResults[0].title}`);
        }

        results.push(...validJiraResults);
      }
    } catch (error) {
      const errorMsg = `Jira search error: ${error.message}`;
      log('ERROR', errorMsg, error);
      errors.push({ source: 'Jira', error: errorMsg });
    }
  }

  if (integrations.confluence && integrations.confluence.enabled && (activeFilters.length === 0 || activeFilters.includes('confluence'))) {
    log('INFO', 'Starting Confluence search', sanitizedIntegrations.confluence);
    try {
      if (!integrations.confluence.baseUrl || !integrations.confluence.apiToken) {
        const error = 'Confluence configuration is incomplete. Please check your settings.';
        log('ERROR', error);
        errors.push({ source: 'Confluence', error });
      } else {
        // Add more detailed logging
        log('INFO', `Searching Confluence for: "${query}"`);

        const confluenceResults = await confluenceSearch(query, integrations.confluence);

        // Ensure confluenceResults is always an array
        const validConfluenceResults = Array.isArray(confluenceResults) ? confluenceResults : [];

        log('INFO', `Confluence search completed with ${validConfluenceResults.length} results`);

        if (validConfluenceResults.length === 0) {
          log('WARN', 'Confluence search returned zero results. This might indicate a permission issue or configuration problem.');
        } else {
          // Log the first result for debugging
          log('INFO', `First Confluence result: ${validConfluenceResults[0].id} - ${validConfluenceResults[0].title}`);
        }

        results.push(...validConfluenceResults);
      }
    } catch (error) {
      const errorMsg = `Confluence search error: ${error.message}`;
      log('ERROR', errorMsg, error);
      errors.push({ source: 'Confluence', error: errorMsg });
    }
  }

  if (integrations.azureDevops.enabled && (activeFilters.length === 0 || activeFilters.includes('azureDevops'))) {
    log('INFO', 'Starting Azure DevOps search', sanitizedIntegrations.azureDevops);
    try {
      if (!integrations.azureDevops.organization || !integrations.azureDevops.project || !integrations.azureDevops.personalAccessToken) {
        const error = 'Azure DevOps configuration is incomplete. Please check your settings.';
        log('ERROR', error);
        errors.push({ source: 'Azure DevOps', error });
      } else {
        // Add more detailed logging
        log('INFO', `Searching Azure DevOps for: "${query}"`);

        const azureResults = await azureSearch(query, integrations.azureDevops);

        log('INFO', `Azure DevOps search completed with ${azureResults.length} results`);
        results.push(...azureResults);
      }
    } catch (error) {
      const errorMsg = `Azure DevOps search error: ${error.message}`;
      log('ERROR', errorMsg, error);
      errors.push({ source: 'Azure DevOps', error: errorMsg });
    }
  }

  if (integrations.asana && integrations.asana.enabled && (activeFilters.length === 0 || activeFilters.includes('asana'))) {
    log('INFO', 'Starting Asana search', sanitizedIntegrations.asana);
    try {
      if (!integrations.asana.personalAccessToken) {
        const error = 'Asana configuration is incomplete. Please check your settings.';
        log('ERROR', error);
        errors.push({ source: 'Asana', error });
      } else {
        // Add more detailed logging
        log('INFO', `Searching Asana for: "${query}"`);

        const asanaResponse = await searchAsana(query, integrations.asana);

        // Ensure asanaResults is always an array
        const asanaResults = asanaResponse.results || [];

        log('INFO', `Asana search completed with ${asanaResults.length} results`);

        if (asanaResults.length === 0) {
          log('WARN', 'Asana search returned zero results. This might indicate a permission issue or configuration problem.');
        } else {
          // Log the first result for debugging
          log('INFO', `First Asana result: ${asanaResults[0].title}`);
        }

        results.push(...asanaResults);
      }
    } catch (error) {
      const errorMsg = `Asana search error: ${error.message}`;
      log('ERROR', errorMsg, error);
      errors.push({ source: 'Asana', error: errorMsg });
    }
  }

  log('INFO', `Search completed with ${results.length} total results and ${errors.length} errors`);

  let finalResults = [];

  try {
    // Perform scoring and sorting based on the chosen mode
    if (sortMode === 'date') {
      log('INFO', 'Sorting results by date (newest first)');
      finalResults = sortResultsByDate(results);
      // Optional: Could still calculate semantic scores here for display purposes if needed
      // without sorting by them. For now, keep it simple.
    } else { // Default to 'relevance'
      log('INFO', 'Processing and sorting results by relevance (semantic + recency boost)');
      finalResults = await processResultsWithSemanticScoring(results, query);
    }

    log('INFO', `Sorting complete. Sending ${finalResults.length} results.`);

    event.reply('search-results', {
      results: finalResults,
      errors
    });
  } catch (processingError) {
    log('ERROR', `Failed to process/sort results with mode ${sortMode}`, processingError);
    // Send original, unsorted results if processing/sorting fails
    event.reply('search-results', {
      results: results,
      errors: [...errors, { source: 'Sorting', error: `Failed to process/sort results: ${processingError.message}` }]
    });
  }
});

// IPC handlers for settings
ipcMain.on('get-settings', (event) => {
  log('INFO', 'Settings requested');
  event.reply('settings', store.get());
});

ipcMain.on('update-setting', (event, key, value) => {
  log('INFO', `Updating setting: ${key}`);

  // Handle nested keys like 'integrations.jira.enabled'
  if (key.includes('.')) {
    const keys = key.split('.');
    let current = store.get();
    let currentPath = '';

    // Navigate to the nested property
    for (let i = 0; i < keys.length - 1; i++) {
      currentPath = currentPath ? `${currentPath}.${keys[i]}` : keys[i];
      if (!current[keys[i]]) {
        // Create the object path if it doesn't exist
        store.set(currentPath, {});
      }
      current = current[keys[i]];
    }

    // Set the final property
    store.set(key, value);

    // Special handling for shortcut changes
    if (key === 'shortcut') {
      globalShortcut.unregisterAll();
      globalShortcut.register(value, () => {
        createSearchWindow();
      });
      log('INFO', `Shortcut changed to ${value}`);
    }
  } else {
    // Handle top-level keys
    store.set(key, value);

    // Special handling for shortcut changes
    if (key === 'shortcut') {
      globalShortcut.unregisterAll();
      globalShortcut.register(value, () => {
        createSearchWindow();
      });
      log('INFO', `Shortcut changed to ${value}`);
    }
  }

  // Notify the renderer that the setting was updated
  event.reply('setting-updated', key, value);
});

// IPC handlers for log file
ipcMain.on('get-log-path', (event) => {
  log('INFO', 'Log path requested');
  event.reply('log-path', logFile);
});

ipcMain.on('open-log-file', (event) => {
  log('INFO', 'Opening log file');
  try {
    // Make sure the log file exists
    if (!fs.existsSync(logFile)) {
      fs.writeFileSync(logFile, 'Log file created\n');
    }

    // Open the log file with the default application
    shell.openPath(logFile)
      .then(result => {
        if (result) {
          log('ERROR', `Failed to open log file: ${result}`);
        }
      })
      .catch(error => {
        log('ERROR', `Error opening log file: ${error.message}`);
      });
  } catch (error) {
    log('ERROR', `Error handling open log file request: ${error.message}`);
  }
});

// Handle external URL opening
ipcMain.on('open-url', (event, url) => {
  log('INFO', `Opening URL: ${url}`);
  shell.openExternal(url);
});

// Handle show window request from tray
ipcMain.on('show-window', (event) => {
  log('INFO', 'Show window requested');
  if (mainWindow) {
    if (mainWindow.isMinimized()) mainWindow.restore();
    mainWindow.show();
    mainWindow.focus();
  } else {
    createMainWindow();
  }
});

// Handle hide window request
ipcMain.on('hide-window', (event) => {
  log('INFO', 'Hide window requested');
  if (mainWindow) {
    mainWindow.hide();
    if (!tray) createTray();
  }
});

// IPC handlers for testing connections
ipcMain.on('test-jira-connection', async (event, config) => {
  // Create a sanitized copy of the config for logging
  const sanitizedConfig = { ...config };
  if (sanitizedConfig.apiToken) sanitizedConfig.apiToken = '***';
  if (sanitizedConfig.email) sanitizedConfig.email = sanitizedConfig.email.replace(/^(.{2})(.*)(@.*)$/, '$1***$3');

  log('INFO', 'Testing Jira connection', sanitizedConfig);

  try {
    if (!config.baseUrl || !config.apiToken) {
      log('ERROR', 'Jira configuration is incomplete');
      event.reply('test-result', {
        source: 'Jira',
        success: false,
        message: 'Configuration is incomplete. Please provide both Base URL and API Token.'
      });
      return;
    }

    // Validate URL format
    if (!config.baseUrl.match(/^https:\/\/[a-zA-Z0-9-]+\.atlassian\.net\/?$/)) {
      log('ERROR', 'Jira URL format is invalid');
      event.reply('test-result', {
        source: 'Jira',
        success: false,
        message: 'URL format is invalid. It should be in the format: https://your-domain.atlassian.net'
      });
      return;
    }

    // Validate email if provided
    if (config.email && !config.email.includes('@')) {
      log('ERROR', 'Jira email format is invalid');
      event.reply('test-result', {
        source: 'Jira',
        success: false,
        message: 'Email format is invalid. Please provide a valid email address.'
      });
      return;
    }

    // Instead of using the /myself endpoint, let's use a simple JQL search
    // which we know works with our authentication method
    // Use a simple test query that should work on any Jira instance
    // We're just testing the connection, not expecting actual results
    const testResults = await jiraSearch('test', config);

    log('INFO', 'Jira connection test successful');
    event.reply('test-result', {
      source: 'Jira',
      success: true,
      message: `Connection successful. Found ${testResults.length} test results.`
    });
  } catch (error) {
    log('ERROR', `Jira connection test failed: ${error.message}`);

    // Provide more helpful error messages
    let message = error.message || 'Unknown error';

    if (message.includes('401') || message.includes('Unauthorized')) {
      message = 'Authentication failed. Please check your API token and ensure it has the necessary permissions.';
    } else if (message.includes('403') || message.includes('Forbidden')) {
      message = 'Authentication failed. Please check your API token and ensure it has the necessary permissions.';
    } else if (message.includes('404') || message.includes('Not Found')) {
      message = 'The Jira URL could not be found. Please verify your Jira domain.';
    } else if (message.includes('timeout') || message.includes('ECONNREFUSED')) {
      message = 'Connection timed out. Please check your network connection and Jira URL.';
    }

    event.reply('test-result', {
      source: 'Jira',
      success: false,
      message: `Jira error: ${message}`
    });
  }
});

// IPC handler for testing Confluence connection
ipcMain.on('test-confluence-connection', async (event, config) => {
  // Create a sanitized copy of the config for logging
  const sanitizedConfig = { ...config };
  if (sanitizedConfig.apiToken) sanitizedConfig.apiToken = '***';
  if (sanitizedConfig.email) sanitizedConfig.email = sanitizedConfig.email.replace(/^(.{2})(.*)(@.*)$/, '$1***$3');

  log('INFO', 'Testing Confluence connection', sanitizedConfig);

  try {
    if (!config.baseUrl || !config.apiToken) {
      log('ERROR', 'Confluence configuration is incomplete');
      event.reply('test-result', {
        source: 'Confluence',
        success: false,
        message: 'Configuration is incomplete. Please provide both Base URL and API Token.'
      });
      return;
    }

    // Validate URL format
    if (!config.baseUrl.match(/^https:\/\/[a-zA-Z0-9-]+\.atlassian\.net\/?$/)) {
      log('ERROR', 'Confluence URL format is invalid');
      event.reply('test-result', {
        source: 'Confluence',
        success: false,
        message: 'URL format is invalid. It should be in the format: https://your-domain.atlassian.net'
      });
      return;
    }

    // Validate email if provided
    if (config.email && !config.email.includes('@')) {
      log('ERROR', 'Confluence email format is invalid');
      event.reply('test-result', {
        source: 'Confluence',
        success: false,
        message: 'Email format is invalid. Please provide a valid email address.'
      });
      return;
    }

    // Use the search function to test the connection
    // Use a simple test query that should work on any Confluence instance
    const testResults = await confluenceSearch('test', config);

    log('INFO', 'Confluence connection test successful');
    event.reply('test-result', {
      source: 'Confluence',
      success: true,
      message: `Connection successful. Found ${testResults.length} test results.`
    });
  } catch (error) {
    log('ERROR', `Confluence connection test failed: ${error.message}`);

    // Provide more helpful error messages
    let message = error.message || 'Unknown error';

    if (message.includes('401') || message.includes('Unauthorized')) {
      message = 'Authentication failed. Please check your API token and ensure it has the necessary permissions.';
    } else if (message.includes('403') || message.includes('Forbidden')) {
      message = 'Authentication failed. Please check your API token and ensure it has the necessary permissions.';
    } else if (message.includes('404') || message.includes('Not Found')) {
      message = 'The Confluence URL could not be found. Please verify your Confluence domain.';
    } else if (message.includes('timeout') || message.includes('ECONNREFUSED')) {
      message = 'Connection timed out. Please check your network connection and Confluence URL.';
    }

    event.reply('test-result', {
      source: 'Confluence',
      success: false,
      message: `Confluence error: ${message}`
    });
  }
});

ipcMain.on('test-azure-connection', async (event, config) => {
  // Create a sanitized copy of the config for logging
  const sanitizedConfig = { ...config };
  if (sanitizedConfig.personalAccessToken) sanitizedConfig.personalAccessToken = '***';

  log('INFO', 'Testing Azure DevOps connection', sanitizedConfig);

  try {
    if (!config.organization || !config.project || !config.personalAccessToken) {
      log('ERROR', 'Azure DevOps configuration is incomplete');
      event.reply('test-result', {
        source: 'Azure',
        success: false,
        message: 'Configuration is incomplete. Please provide Organization, Project, and Personal Access Token.'
      });
      return;
    }

    // Validate PAT format (should be a non-empty string)
    if (!config.personalAccessToken || config.personalAccessToken.trim() === '') {
      log('ERROR', 'Azure DevOps PAT is empty');
      event.reply('test-result', {
        source: 'Azure',
        success: false,
        message: 'Personal Access Token cannot be empty.'
      });
      return;
    }

    // Instead of using the search function, let's make a direct API call to test the connection
    const token = Buffer.from(`:${config.personalAccessToken}`).toString('base64');

    // Use the search API with a minimal query to test authentication
    const testUrl = `https://almsearch.dev.azure.com/${config.organization}/${config.project}/_apis/search/workitemsearchresults?api-version=7.1`;

    log('INFO', `Testing connection to Azure DevOps organization: ${config.organization}, project: ${config.project}`);

    const response = await axios.post(testUrl, {
      searchText: "test",
      $skip: 0,
      $top: 1,
      filters: {
        "System.TeamProject": [config.project]
      }
    }, {
      headers: {
        'Authorization': `Basic ${token}`, // token is already sanitized before logging
        'Content-Type': 'application/json'
      },
      timeout: 10000,
      validateStatus: false
    });

    if (response.status === 200) {
      log('INFO', 'Azure DevOps connection test successful');
      event.reply('test-result', {
        source: 'Azure',
        success: true,
        message: 'Connection successful! Your Azure DevOps integration is working properly.'
      });
    } else {
      throw new Error(`API returned status ${response.status}: ${response.statusText || 'Unknown error'}`);
    }
  } catch (error) {
    log('ERROR', `Azure DevOps connection test failed: ${error.message}`);
    let errorMessage = error.message || 'Unknown error';

    // Provide more helpful error messages
    if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
      errorMessage = 'Authentication failed. Please check your Personal Access Token. Make sure it has "Read" access to "Work Items" and "Code". For search functionality, it may need additional permissions.';
    } else if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
      errorMessage = 'Authentication failed. Your PAT may not have the required permissions for this operation.';
    } else if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
      errorMessage = 'Organization or Project not found. Please verify the names are correct.';
    } else if (errorMessage.includes('ENOTFOUND') || errorMessage.includes('ETIMEDOUT')) {
      errorMessage = 'Connection failed. Please check your network connection and Azure DevOps URL.';
    }

    event.reply('test-result', {
      source: 'Azure',
      success: false,
      message: 'Azure DevOps error: ' + errorMessage
    });
  }
});

// IPC handler for testing Asana connection
ipcMain.on('test-asana-connection', async (event, config) => {
  // Create a sanitized copy of the config for logging
  const sanitizedConfig = { ...config };
  if (sanitizedConfig.personalAccessToken) sanitizedConfig.personalAccessToken = '***';

  log('INFO', 'Testing Asana connection', sanitizedConfig);

  try {
    if (!config.personalAccessToken) {
      log('ERROR', 'Asana configuration is incomplete');
      event.reply('test-result', {
        source: 'Asana',
        success: false,
        message: 'Configuration is incomplete. Please provide a Personal Access Token.'
      });
      return;
    }

    // Validate PAT format (should be a non-empty string)
    if (!config.personalAccessToken || config.personalAccessToken.trim() === '') {
      log('ERROR', 'Asana PAT is empty');
      event.reply('test-result', {
        source: 'Asana',
        success: false,
        message: 'Personal Access Token cannot be empty.'
      });
      return;
    }

    // Make a direct API call to test the connection
    // Use the /users/me endpoint to test authentication
    const testUrl = 'https://app.asana.com/api/1.0/users/me';

    log('INFO', 'Testing connection to Asana API');

    const response = await axios.get(testUrl, {
      headers: {
        'Authorization': `Bearer ${config.personalAccessToken}`,
        'Accept': 'application/json'
      },
      timeout: 10000,
      validateStatus: false
    });

    if (response.status === 200) {
      log('INFO', 'Asana connection test successful');

      // If workspace and project are provided, verify they exist
      let additionalMessage = '';

      if (config.workspace) {
        try {
          const workspaceResponse = await axios.get(`https://app.asana.com/api/1.0/workspaces/${config.workspace}`, {
            headers: {
              'Authorization': `Bearer ${config.personalAccessToken}`,
              'Accept': 'application/json'
            },
            timeout: 5000,
            validateStatus: false
          });

          if (workspaceResponse.status === 200) {
            additionalMessage += ` Workspace verified.`;
          } else {
            additionalMessage += ` Warning: Could not verify workspace ID.`;
          }
        } catch (workspaceError) {
          additionalMessage += ` Warning: Could not verify workspace ID.`;
        }
      }

      if (config.project) {
        try {
          const projectResponse = await axios.get(`https://app.asana.com/api/1.0/projects/${config.project}`, {
            headers: {
              'Authorization': `Bearer ${config.personalAccessToken}`,
              'Accept': 'application/json'
            },
            timeout: 5000,
            validateStatus: false
          });

          if (projectResponse.status === 200) {
            additionalMessage += ` Project verified.`;
          } else {
            additionalMessage += ` Warning: Could not verify project ID.`;
          }
        } catch (projectError) {
          additionalMessage += ` Warning: Could not verify project ID.`;
        }
      }

      event.reply('test-result', {
        source: 'Asana',
        success: true,
        message: `Connection successful! Your Asana integration is working properly.${additionalMessage}`
      });
    } else {
      throw new Error(`API returned status ${response.status}: ${response.statusText || 'Unknown error'}`);
    }
  } catch (error) {
    log('ERROR', `Asana connection test failed: ${error.message}`);
    let errorMessage = error.message || 'Unknown error';

    // Provide more helpful error messages
    if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
      errorMessage = 'Authentication failed. Please check your Personal Access Token. Make sure it has the necessary permissions.';
    } else if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
      errorMessage = 'Authentication failed. Your Personal Access Token may not have the required permissions for this operation.';
    } else if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
      errorMessage = 'Resource not found. Please verify your workspace or project IDs if provided.';
    } else if (errorMessage.includes('ENOTFOUND') || errorMessage.includes('ETIMEDOUT')) {
      errorMessage = 'Connection failed. Please check your network connection.';
    }

    event.reply('test-result', {
      source: 'Asana',
      success: false,
      message: 'Asana error: ' + errorMessage
    });
  }
});

// -----------------------------------------------------------------------------
// Semantic Search Scoring Implementation
// -----------------------------------------------------------------------------

// Model pipeline instance (lazy loaded)
let extractor = null;
// Model name - choose a suitable sentence transformer
const modelName = 'Xenova/all-MiniLM-L6-v2';

// Function to calculate cosine similarity between two embeddings (vectors)
function calculateCosineSimilarity(embedding1, embedding2) {
  // Ensure embeddings are valid Float32Arrays and have the same length
  if (!(embedding1 instanceof Float32Array) || !(embedding2 instanceof Float32Array) || embedding1.length !== embedding2.length) {
    log('WARN', 'Invalid embeddings for cosine similarity calculation');
    return 0; // Return 0 similarity for invalid input
  }

  let dotProduct = 0;
  let magnitude1 = 0;
  let magnitude2 = 0;

  for (let i = 0; i < embedding1.length; i++) {
    dotProduct += embedding1[i] * embedding2[i];
    magnitude1 += embedding1[i] ** 2;
    magnitude2 += embedding2[i] ** 2;
  }

  magnitude1 = Math.sqrt(magnitude1);
  magnitude2 = Math.sqrt(magnitude2);

  if (magnitude1 === 0 || magnitude2 === 0) {
    return 0; // Avoid division by zero
  }

  return dotProduct / (magnitude1 * magnitude2);
}

/**
 * Calculate a recency boost multiplier based on the age of the item.
 * Uses exponential decay: boost starts high for recent items and decays towards 1.0.
 * @param {string | Date} updatedDate - The last updated date of the result.
 * @returns {number} - A boost multiplier (>= 1.0).
 */
function calculateRecencyBoost(updatedDate) {
  if (!updatedDate) return 1.0; // No date, no boost

  try {
    const itemDate = new Date(updatedDate);
    const now = new Date();
    // Calculate difference in milliseconds, handle potential invalid dates
    const diffMillis = now.getTime() - itemDate.getTime();
    if (isNaN(diffMillis) || diffMillis < 0) return 1.0; // Invalid date or future date

    const daysDiff = diffMillis / (1000 * 60 * 60 * 24);

    // Exponential decay parameters (tune these as needed)
    const maxBoost = 1.0; // Max additional boost (1.0 means up to 2.0x multiplier)
    const decayRate = 0.1; // Slightly faster decay rate
    // Based on wanting boost near 1.0 after ~30 days: exp(-decayRate * 30) ≈ 0.1

    const boost = 1.0 + maxBoost * Math.exp(-decayRate * daysDiff);

    // Clamp boost just in case (shouldn't be necessary with exp decay > 0)
    return Math.max(1.0, boost);

  } catch (e) {
    log('WARN', 'Error parsing date for recency boost', { date: updatedDate, error: e.message });
    return 1.0; // Error parsing date, no boost
  }
}

/**
 * Lazily initialize the sentence transformer pipeline
 */
async function getExtractor() {
  if (!extractor) {
    try {
      log('INFO', `Initializing sentence transformer model: ${modelName}`);
      // Dynamically import the pipeline function when needed
      const { pipeline } = await import('@xenova/transformers');
      // Initialize the pipeline for feature extraction (embeddings)
      extractor = await pipeline('feature-extraction', modelName);
      log('INFO', 'Sentence transformer model initialized successfully.');
    } catch (error) {
      log('ERROR', 'Failed to initialize sentence transformer model', error);
      // Set extractor back to null so we might retry later
      extractor = null;
      throw new Error(`Failed to load embedding model: ${error.message}`);
    }
  }
  return extractor;
}

/**
 * Process search results using semantic similarity scoring
 * @param {Array} combinedResults - Array of results from all integrations
 * @param {string} query - The original search query
 * @returns {Promise<Array>} - Semantically scored and sorted results
 */
async function processResultsWithSemanticScoring(combinedResults, query) {
  log('INFO', `Processing ${combinedResults.length} combined results with semantic scoring system`);

  if (!combinedResults || combinedResults.length === 0) {
    return []; // Return empty if no results
  }
  if (!query) {
    log('WARN', 'No query provided for semantic scoring, returning original order.');
    return combinedResults; // Return original order if no query
  }

  const model = await getExtractor();
  if (!model) {
    log('ERROR', 'Semantic scoring failed: Model not available.');
    throw new Error('Embedding model is not available for scoring.');
  }

  try {
    // 1. Generate embedding for the query
    log('INFO', `Generating embedding for query: "${query}"`);
    const queryEmbedding = await model(query, { pooling: 'mean', normalize: true });

    // 2. Generate embeddings for each result and calculate similarity
    const scoredResults = [];
    for (const result of combinedResults) {
      // Combine relevant text fields for embedding
      const textToEmbed = `${result.title || ''} ${result.description || ''}`.trim();

      if (!textToEmbed) {
         log('WARN', `Result ${result.id || 'unknown'} has no text content for embedding, assigning score 0.`);
         scoredResults.push({ ...result, semanticScore: 0 });
         continue;
      }

      log('DEBUG', `Generating embedding for result: "${textToEmbed.substring(0, 50)}..."`);
      const resultEmbedding = await model(textToEmbed, { pooling: 'mean', normalize: true });

      // Calculate cosine similarity
      // The model output embeddings are typically nested, e.g., resultEmbedding.data
      const similarity = calculateCosineSimilarity(queryEmbedding.data, resultEmbedding.data);

      // Clamp similarity score between 0 and 1 (though cosine similarity is usually -1 to 1)
      const semanticScore = Math.max(0, Math.min(1, similarity));

      // Calculate recency boost
      const recencyBoost = calculateRecencyBoost(result.updated);

      // Combine semantic score and recency boost
      const finalScore = semanticScore * recencyBoost;

      scoredResults.push({
        ...result,
        semanticScore: semanticScore, // Store original semantic score
        recencyBoost: recencyBoost,   // Store boost factor
        finalScore: finalScore,       // Store combined score for sorting
        semanticScoreDisplay: Math.round(semanticScore * 100)
      });
    }

    // 3. Sort results by the combined final score (highest first)
    scoredResults.sort((a, b) => (b.finalScore || 0) - (a.finalScore || 0));

    log('INFO', `Semantic scoring complete. Top result final score: ${scoredResults[0]?.finalScore?.toFixed(4)} (Semantic: ${scoredResults[0]?.semanticScore?.toFixed(4)}, Boost: ${scoredResults[0]?.recencyBoost?.toFixed(2)})`);

    return scoredResults;

  } catch (error) {
    log('ERROR', 'Error during semantic scoring', error);
    // Re-throw the error to be caught by the caller in the IPC handler
    throw new Error(`Semantic scoring failed: ${error.message}`);
  }
}

/**
 * Sorts an array of results strictly by their 'updated' date, newest first.
 * Handles missing or invalid dates by pushing them to the end.
 * @param {Array} results - The array of search results.
 * @returns {Array} - The sorted array of results.
 */
function sortResultsByDate(results) {
  return results.sort((a, b) => {
    const dateA = a.updated ? new Date(a.updated) : null;
    const dateB = b.updated ? new Date(b.updated) : null;

    // Handle invalid dates: push them to the bottom
    const isValidA = dateA && !isNaN(dateA);
    const isValidB = dateB && !isNaN(dateB);

    if (isValidA && isValidB) {
      return dateB.getTime() - dateA.getTime(); // Sort descending (newest first)
    } else if (isValidA) {
      return -1; // A is valid, B is not -> A comes first
    } else if (isValidB) {
      return 1; // B is valid, A is not -> B comes first
    } else {
      return 0; // Both invalid, maintain relative order (or consider title sort as fallback)
    }
  });
}